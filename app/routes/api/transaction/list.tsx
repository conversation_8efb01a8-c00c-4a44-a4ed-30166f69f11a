import { data, type LoaderFunctionArgs } from 'react-router';
import { getSession } from '@/session.server';
import { getAPIClient } from '@/lib/api';

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Get session and validate user is authenticated
    const session = await getSession(request.headers.get('Cookie'));
    const address = session.get('address');

    if (!address) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Get API client and fetch transaction list from backend
    const api = getAPIClient();
    const result = await api.getTransactionList({ address });

    // Return the transaction list data
    return data(result.data, { status: 200 });
  } catch (error) {
    console.error('Error in /api/transaction/list:', error);

    // Handle different types of errors
    if (error instanceof Error && error.message.includes('fetch')) {
      return new Response('Backend API unavailable', { status: 503 });
    }

    // Generic server error
    return new Response('Internal Server Error', { status: 500 });
  }
}
