import { data, type ActionFunctionArgs } from 'react-router';
import { getSession } from '@/session.server';
import { getAPIClient, type SignTransactionRequest } from '@/lib/api';

export async function action({ request }: ActionFunctionArgs) {
  try {
    // Get session and validate user is authenticated
    const session = await getSession(request.headers.get('Cookie'));
    const address = session.get('address');

    if (!address) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse request body
    const body: SignTransactionRequest = await request.json();

    // Validate required fields
    if (!body.hash || !body.signature || !body.type) {
      return new Response('Missing required fields: hash, signature, type', { status: 400 });
    }

    // Validate transaction type
    if (body.type !== 'withdraw' && body.type !== 'deposit') {
      return new Response('Invalid transaction type. Must be "withdraw" or "deposit"', {
        status: 400,
      });
    }

    // Get API client and submit signature to backend
    const api = getAPIClient();
    const result = await api.signTransaction(body);

    if (result.data === 'Successfully') {
      const resp = await api.getTransaction({
        hash: body.hash,
        type: body.type,
        address,
      });

      return data(resp.data, { status: 200 });
    } else {
      return new Response(result.data, { status: 400 });
    }
  } catch (error) {
    console.error('Error in /api/transaction/sign:', error);

    // Handle different types of errors
    if (error instanceof SyntaxError) {
      return new Response('Invalid JSON in request body', { status: 400 });
    }

    // If it's a network error from the backend API
    if (error instanceof Error && error.message.includes('fetch')) {
      return new Response('Backend API unavailable', { status: 503 });
    }

    // Generic server error
    return new Response('Internal Server Error', { status: 500 });
  }
}
