import { data, type ActionFunctionArgs } from 'react-router';
import { JsonRpcProvider } from 'ethers';
import { SiweErrorType, SiweMessage } from 'siwe';
import { commitSession, getSession } from '@/session.server';
import { getAPIClient } from '@/lib/api';
import { rpcUrl } from '@/config';

type SignRequestBody = {
  ens: string;
  message: string;
  signature: string;
};

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request.headers.get('Cookie'));
  const body: SignRequestBody = await request.json();

  const provider = new JsonRpcProvider(rpcUrl);
  const message = new SiweMessage(body.message);

  const api = getAPIClient();

  try {
    const { data: fields } = await message.verify(
      {
        signature: body.signature,
        nonce: session.get('nonce'),
      },
      { provider },
    );

    const {
      data: { list: committees },
    } = await api.getCommittees();

    if (
      !committees
        .map((address) => address.toLocaleLowerCase())
        .includes(fields.address.toLocaleLowerCase())
    ) {
      return new Response('Unauthorized', { status: 401 });
    }

    session.set('address', fields.address);
    session.unset('nonce');

    return new Response(JSON.stringify(fields), {
      status: 200,
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    });
  } catch (error) {
    console.error(error);
    switch (error) {
      case SiweErrorType.EXPIRED_MESSAGE:
        return new Response(error, { status: 400 });
      case SiweErrorType.INVALID_SIGNATURE:
        return new Response(error, { status: 401 });
      case SiweErrorType.INVALID_NONCE:
        return new Response(error, { status: 401 });
      default:
        return new Response(`${error}`, { status: 400 });
    }
  }
}
