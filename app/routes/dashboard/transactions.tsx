import { TransactionCard } from '@/components/transaction-card';
import { getAPIClient, type Transaction } from '@/lib/api';
import { getSession } from '@/session.server';
import type { Handle } from '@/types';
import { useQuery } from '@tanstack/react-query';
import ky from 'ky';
import { useLoaderData, type LoaderFunctionArgs } from 'react-router';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request.headers.get('Cookie'));

  const address = session.get('address');
  if (!address) {
    throw new Response('Unauthorized', { status: 401 });
  }

  const api = getAPIClient();
  const { data: transactions } = await api.getTransactionList({ address });

  return { transactions };
}

export default function DashboardTransactionsPage() {
  const { transactions } = useLoaderData<typeof loader>();

  const { data } = useQuery({
    queryKey: ['signer/transactions'],
    queryFn: async () => {
      return ky.get('/api/transaction/list').json<Transaction[]>();
    },
    initialData: transactions,
  });

  return (
    <div className="grid lg:grid-cols-2 2xl:grid-cols-3 gap-4">
      {data.map((transaction) => (
        <TransactionCard key={transaction.hash} transaction={transaction} />
      ))}
    </div>
  );
}

export const handle: Handle = {
  breadcrumb: {
    title: 'Transactions',
    url: '/signer/transactions',
  },
};
