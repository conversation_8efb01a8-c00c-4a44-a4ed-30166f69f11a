import { TransactionCard } from '@/components/transaction-card';
import { getAPIClient } from '@/lib/api';
import { getSession } from '@/session.server';
import type { Handle } from '@/types';
import { useLoaderData, type LoaderFunctionArgs } from 'react-router';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request.headers.get('Cookie'));

  const address = session.get('address');
  if (!address) {
    throw new Response('Unauthorized', { status: 401 });
  }

  const api = getAPIClient();
  const { data: transactions } = await api.getTransactionList({ address });

  return { transactions };
}

export default function DashboardTransactionsPage() {
  const { transactions } = useLoaderData<typeof loader>();

  return (
    <div className="grid lg:grid-cols-2 2xl:grid-cols-3 gap-4">
      {transactions.map((transaction) => (
        <TransactionCard key={transaction.hash} transaction={transaction} />
      ))}
    </div>
  );
}

export const handle: Handle = {
  breadcrumb: {
    title: 'Transactions',
    url: '/signer/transactions',
  },
};
