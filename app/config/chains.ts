import { BitlayerRoundedIcon } from '@/components/icons/bitlayer';
import { SuiIcon } from '@/components/icons/sui';
import type { EV<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/types';
import { SuiMainnetChain, SuiTestnetChain } from '@suiet/wallet-kit';
import { btr, btrTestnet } from 'viem/chains';

export const bitlayerTestnet: EVMChain = {
  ...btrTestnet,
  icon: BitlayerRoundedIcon,
  type: 'evm',
  chainId: 4,
  contracts: {
    bridge: { address: '******************************************' },
  },
};

export const suiTestnet: SuiChain = {
  ...SuiTestnetChain,
  icon: SuiIcon,
  type: 'sui',
  chainId: 16,
  contracts: {
    bridge: {
      address: '0xa77381f9d9677cd019e37c746016f768b6bf8791d1562af333b8e682867cf2c3',
      package: '0x1e9255efc83c1d9d4c30a89fefc2071e1154ec12197d906ade6e4c5ff96e5a7a',
    },
  },
  // blockExplorers: {
  //   default: {
  //     name: 'SuiVision',
  //     url: 'https://testnet.suivision.xyz/',
  //   },
  // },
};

export const bitlayerMainnet: EVMChain = {
  ...btr,
  icon: BitlayerRoundedIcon,
  type: 'evm',
  chainId: 4,
  contracts: {},
};

export const suiMainnet: SuiChain = {
  ...SuiMainnetChain,
  icon: SuiIcon,
  type: 'sui',
  chainId: 16,
  contracts: {},
};

export const ChainMap = {
  [bitlayerTestnet.chainId]: bitlayerTestnet,
  [bitlayerMainnet.chainId]: bitlayerMainnet,
  [suiTestnet.chainId]: suiTestnet,
  [suiMainnet.chainId]: suiMainnet,
};
