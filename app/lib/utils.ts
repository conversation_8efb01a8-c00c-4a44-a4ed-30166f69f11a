import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { EV<PERSON>hai<PERSON>, <PERSON><PERSON><PERSON>hai<PERSON> } from '@/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export type ShortenAddressOptions = {
  prefixLength?: number;
  suffixLength?: number;
};

export function shortenAddress(address: string | undefined, options: ShortenAddressOptions = {}) {
  if (!address) return '';
  const { prefixLength = 6, suffixLength = 4 } = options;
  return `${address.slice(0, prefixLength)}...${address.slice(-suffixLength)}`;
}

/**
 * Build a block explorer link for an address
 * @param chain - The blockchain chain (EVMChain or SuiChain)
 * @param address - The address to link to
 * @returns The block explorer URL for the address
 */
export function buildAddressLink(chain: EVMChain | SuiChain, address: string): string {
  if (chain.type === 'evm') {
    // For EVM chains, use the blockExplorers configuration
    const baseUrl = chain.blockExplorers?.default?.url;
    if (baseUrl) {
      return `${baseUrl}/address/${address}`;
    }

    // Generic fallback
    return `#address-${address}`;
  } else {
    // For Sui chains, use chain id to determine network
    if (chain.id === 'sui:testnet') {
      return `https://testnet.suivision.xyz/account/${address}`;
    } else {
      return `https://suivision.xyz/account/${address}`;
    }
  }
}

/**
 * Build a block explorer link for a transaction
 * @param chain - The blockchain chain (EVMChain or SuiChain)
 * @param hash - The transaction hash to link to
 * @returns The block explorer URL for the transaction
 */
export function buildTxLink(chain: EVMChain | SuiChain, hash: string): string {
  if (chain.type === 'evm') {
    // For EVM chains, use the blockExplorers configuration
    const baseUrl = chain.blockExplorers?.default?.url;
    if (baseUrl) {
      return `${baseUrl}/tx/${hash}`;
    }
    // Generic fallback
    return `#tx-${hash}`;
  } else {
    // For Sui chains, use chain id to determine network
    if (chain.id === 'sui:testnet') {
      return `https://testnet.suivision.xyz/txblock/${hash}`;
    } else {
      return `https://suivision.xyz/txblock/${hash}`;
    }
  }
}
