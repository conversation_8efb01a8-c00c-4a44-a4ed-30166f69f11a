import ky from 'ky';

// Types for API responses
export interface ApiResponse<T> {
  code: number;
  data: T;
}

export interface Committee {
  list: string[];
}

export interface Transaction {
  hash: string;
  from_address: string;
  original_amount: number;
  amount: number;
  fee: number;
  status: 'new' | 'signed' | 'finished' | 'failed';
  created_at: number;
  updated_at: number;
  current_process: number;
  total_process: number;
  type: 'withdraw' | 'deposit';
  sign_msg: string;
  target_hash: string;
}

export interface SignTransactionRequest {
  hash: string;
  signature: string;
  type: 'withdraw' | 'deposit';
}

export interface GetTransactionParams {
  hash: string;
  type: 'withdraw' | 'deposit';
  address: string;
}

export interface GetTransactionListParams {
  address: string;
}

/**
 * APIClient class for interacting with the Bitnova API
 */
export class APIClient {
  private api: typeof ky;

  constructor(baseUrl: string = '') {
    this.api = ky.create({
      prefixUrl: baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 300000, // 30 seconds timeout
    });
  }

  /**
   * Get committee list
   * @returns Promise<ApiResponse<Committee>>
   */
  async getCommittees(): Promise<ApiResponse<Committee>> {
    return this.api.get('bitnova/committees').json<ApiResponse<Committee>>();
  }

  /**
   * Get transaction list for a specific address
   * @param params - Parameters containing the address
   * @returns Promise<ApiResponse<Transaction[]>>
   */
  async getTransactionList(params: GetTransactionListParams): Promise<ApiResponse<Transaction[]>> {
    return this.api
      .get('bitnova/transaction/list', {
        searchParams: {
          address: params.address,
        },
      })
      .json<ApiResponse<Transaction[]>>();
  }

  /**
   * Submit a transaction signature
   * @param data - Transaction signature data
   * @returns Promise<ApiResponse<string>>
   */
  async signTransaction(data: SignTransactionRequest): Promise<ApiResponse<string>> {
    return this.api
      .post('bitnova/transaction/sign', {
        json: data,
      })
      .json<ApiResponse<string>>();
  }

  /**
   * Get details of a specific transaction
   * @param params - Parameters containing hash, type, and address
   * @returns Promise<ApiResponse<Transaction>>
   */
  async getTransaction(params: GetTransactionParams): Promise<ApiResponse<Transaction>> {
    return this.api
      .get('bitnova/transaction/get_one', {
        searchParams: {
          hash: params.hash,
          type: params.type,
          address: params.address,
        },
      })
      .json<ApiResponse<Transaction>>();
  }
}

// Singleton instance
let apiClientInstance: APIClient | null = null;

/**
 * Get the singleton instance of APIClient
 * @param baseUrl - Optional base URL for the API (only used on first call)
 * @returns APIClient instance
 */
export function getAPIClient(
  baseUrl: string = 'https://test-bitnova-bridge-api.bitlayerapps.org',
): APIClient {
  if (!apiClientInstance) {
    apiClientInstance = new APIClient(baseUrl);
  }
  return apiClientInstance;
}

// Export default instance getter for convenience
export default getAPIClient;
