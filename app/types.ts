import type { Address, ChainContract, Chain as OriginalEvmChain } from 'viem';
import type { Chain as OriginalSuiChain } from '@suiet/wallet-kit';

export type Breadcrumb = {
  title: string;
  url: string;
};

export type Handle = {
  breadcrumb?: Breadcrumb;
};

type SVGIcon = React.FC<React.SVGProps<SVGSVGElement>>;

type ExtendedChainData<Type extends 'evm' | 'sui'> = {
  icon?: SVGIcon;
  type: Type;
  chainId: number;
};

type EVMExtendedContractData = {
  contracts: {
    bridge: ChainContract;
  };
};

type SuiExtendedContractData = {
  contracts: {
    bridge: ChainContract & { package: string };
  };
};

export type EVMChain = OriginalEvmChain & ExtendedChainData<'evm'> & EVMExtendedContractData;
export type SuiChain = OriginalSuiChain & ExtendedChainData<'sui'> & SuiExtendedContractData;

export type Chain = EVMChain | SuiChain;

export type NativeToken = {
  id: string;
  name: string;
  symbol: string;
  decimals: number;
  icon: SVGIcon;
  type: 'native';
  tokenId: number;
};

export type ERC20Token = {
  id: string;
  name: string;
  symbol: string;
  decimals: number;
  icon: SVGIcon;
  type: 'erc20';
  contract: Address;
  tokenId: number;
};

export type SuiToken = {
  id: string;
  name: string;
  symbol: string;
  decimals: number;
  icon: SVGIcon;
  type: 'sui';
  packageId: Address;
  moveModule: string;
  struct: string;
  tokenId: number;
};

export type Token = NativeToken | ERC20Token | SuiToken;
