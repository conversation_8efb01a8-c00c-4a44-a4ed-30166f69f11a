import { createContext, useContext } from 'react';
import type { Address } from 'viem';
import { useAccountEffect } from 'wagmi';

type SessionContextData = {
  address?: Address;
};

const sessionContext = createContext<SessionContextData>({});

type SessionProviderProps = {
  address?: Address;
  children: React.ReactNode;
};

export function SessionProvider({ address, children }: SessionProviderProps) {
  useAccountEffect({
    onConnect({ address }) {
      console.log('Connected', address);
    },
    onDisconnect() {
      console.log('Disconnected');
    },
  });

  return <sessionContext.Provider value={{ address }}>{children}</sessionContext.Provider>;
}

export function useSession() {
  return useContext(sessionContext);
}
