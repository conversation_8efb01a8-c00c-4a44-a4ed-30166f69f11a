import ky from 'ky';
import { createContext, useCallback, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router';
import type { Address } from 'viem';
import { useAccount, useAccountEffect } from 'wagmi';

type SessionContextData = {
  address?: Address;
};

const sessionContext = createContext<SessionContextData>({});

type SessionProviderProps = {
  address?: Address;
  children: React.ReactNode;
};

export function SessionProvider({ address, children }: SessionProviderProps) {
  const navigate = useNavigate();

  const logout = useCallback(async () => {
    await ky.post('/api/sign-out');
    navigate('/login');
  }, []);

  useAccountEffect({
    onConnect({ address }) {
      console.log('Connected', address);
    },
    onDisconnect() {
      console.log('Disconnected');
      if (address) {
        logout();
      }
    },
  });

  const { address: currentAddress } = useAccount();
  useEffect(() => {
    if (currentAddress && currentAddress !== address) {
      logout();
    }
  }, [currentAddress]);

  return <sessionContext.Provider value={{ address }}>{children}</sessionContext.Provider>;
}

export function useSession() {
  return useContext(sessionContext);
}
