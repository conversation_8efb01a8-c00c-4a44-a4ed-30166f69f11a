import * as React from 'react';

export const BitlayerIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={21}
    height={19}
    viewBox="0 0 21 19"
    fill="none"
    {...props}
  >
    <path
      d="M.757 1.002v3.552c.803-.425 1.6-.374 2.39-.46V.584c-.867 0-1.587.007-2.39.418ZM7.762 1.395c-.754-.272-1.514-.51-2.28-.663v3.47c.765.14 1.526.364 2.28.623v-3.43Z"
      fill="#E36E1B"
    />
    <path
      d="M5.481 7.67V4.201c-.772-.14-1.55-.194-2.334-.108v3.51c.784-.1 1.562-.06 2.334.067ZM.757 8.105v3.551c.803-.453 1.6-.428 2.39-.543v-3.51c-.82.068-1.587.063-2.39.502ZM5.481 7.67v3.47c.766.113 1.527.311 2.281.544v-3.43c-.754-.245-1.514-.457-2.28-.583Z"
      fill="#E36E1B"
    />
    <path
      d="M3.147 11.113v3.51a8.041 8.041 0 0 1 2.334-.014v-3.47a8.039 8.039 0 0 0-2.334-.026ZM18.355 9.605c.889-.743 1.388-1.936 1.388-3.164 0-1.902-1.167-2.681-3.596-2.922V1.86a15.392 15.392 0 0 1-1.558-.164V3.35a21.562 21.562 0 0 1-1.47-.27V1.412c-.534-.12-1.07-.26-1.61-.41v1.683a70.799 70.799 0 0 1-1.518-.43v3.39c-.737-.273-1.48-.563-2.23-.82v3.43c.75.244 1.493.52 2.23.781v3.391c-.737-.247-1.48-.512-2.23-.743v3.43c.75.218 1.493.469 2.23.704.509.114 1.015.225 1.518.325v1.682c.54.103 1.076.195 1.61.27v-1.669c.492.073.982.131 1.47.169v1.655c.522.036 1.042.047 1.558.03v-1.645c2.694-.148 4.094-2.28 4.094-4.406 0-1.57-.718-2.331-1.886-2.625Zm-5.232-3.797c.491.1.98.184 1.465.248a16.903 16.903 0 0 0 .771.087l.03.003.109.016h.007l.113.021.02.004a2.495 2.495 0 0 1 .22.055c.106.032.202.07.29.114.408.207.616.562.616 1.086 0 .533-.208.912-.617 1.105-.087.04-.184.073-.29.096l-.006.001c-.034.008-.07.014-.107.02h-.004a2.05 2.05 0 0 1-.103.011l-.02.002a2.064 2.064 0 0 1-.112.006h-.146a3.312 3.312 0 0 1-.12-.008 17.582 17.582 0 0 1-.65-.064 21.238 21.238 0 0 1-1.466-.229V5.808Zm3.024 8.115-.096.014-.03.003c-.023.002-.047.005-.072.006l-.034.003-.075.003h-.032a15.795 15.795 0 0 1-1.22-.063 20.908 20.908 0 0 1-1.465-.188v-2.708c.491.087.98.159 1.465.21.372.038.742.065 1.11.078l.11.005.032.002.075.006.034.003.073.008a2.427 2.427 0 0 1 .125.016c.743.112 1.13.485 1.13 1.244 0 .753-.387 1.232-1.13 1.357v.001Z"
      fill="#E36E1B"
    />
  </svg>
);

export const BitlayerRoundedIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={49}
    height={50}
    viewBox="0 0 49 50"
    fill="none"
    {...props}
  >
    <circle cx={24.5} cy={25} r={24.5} fill="#E36E1B" />
    <path
      d="M8.75 11.954v5.742c1.297-.687 2.585-.604 3.862-.744v-5.674c-1.401 0-2.565.012-3.863.676ZM20.075 12.59c-1.22-.44-2.45-.825-3.688-1.072v5.61c1.238.225 2.468.588 3.688 1.007v-5.546ZM16.387 22.735v-5.609c-1.249-.227-2.507-.314-3.774-.174v5.674c1.268-.16 2.525-.096 3.774.11ZM8.75 23.437v5.742c1.297-.732 2.584-.693 3.862-.878v-5.675c-1.324.11-2.565.101-3.863.811ZM16.387 22.735v5.61c1.238.182 2.468.503 3.688.88v-5.546c-1.22-.397-2.45-.74-3.688-.944ZM12.613 28.3v5.675c1.268-.206 2.525-.184 3.774-.022v-5.61c-1.249-.183-2.507-.226-3.774-.043Z"
      fill="#000"
    />
    <path
      d="M37.201 25.862c1.437-1.2 2.245-3.13 2.245-5.114 0-3.076-1.887-4.336-5.814-4.726v-2.68a24.884 24.884 0 0 1-2.52-.267v2.677a34.854 34.854 0 0 1-2.377-.438v-2.697c-.863-.195-1.73-.42-2.602-.662v2.72c-.814-.22-1.632-.455-2.455-.696v5.482c-1.192-.442-2.394-.911-3.604-1.326v5.545c1.21.394 2.412.842 3.604 1.263v5.482c-1.192-.4-2.394-.828-3.604-1.2v5.544c1.21.353 2.412.759 3.604 1.139.823.184 1.641.363 2.455.525v2.72c.872.166 1.74.316 2.602.436v-2.697c.796.117 1.588.211 2.377.271v2.677a24.46 24.46 0 0 0 2.52.048v-2.66c4.354-.238 6.618-3.685 6.618-7.123 0-2.537-1.16-3.768-3.049-4.244v.001Zm-8.459-6.139a34.68 34.68 0 0 0 2.37.402c.35.047.7.087 1.05.119.067.006.132.013.196.021l.048.006.175.025.012.002c.063.01.123.02.183.033l.03.006a4.057 4.057 0 0 1 .347.086c.004 0 .007.002.011.003.17.051.327.113.468.184.661.335.997.909.997 1.755 0 .862-.336 1.476-.997 1.787a2.206 2.206 0 0 1-.468.155l-.011.002a2.785 2.785 0 0 1-.173.031h-.006c-.054.009-.11.015-.167.02l-.031.003c-.06.004-.12.008-.183.009h-.187l-.048-.001a5.224 5.224 0 0 1-.196-.011c-.349-.028-.7-.063-1.05-.104a34.317 34.317 0 0 1-2.37-.37V19.723Zm4.889 13.121a3.833 3.833 0 0 1-.156.022l-.046.005a5.768 5.768 0 0 1-.118.01l-.055.005-.121.005-.052.001a4.337 4.337 0 0 1-.177 0 25.607 25.607 0 0 1-1.795-.102 33.816 33.816 0 0 1-2.369-.305v-4.377c.794.14 1.584.255 2.37.338a26.085 26.085 0 0 0 1.97.135 6.578 6.578 0 0 1 .173.012l.057.005.117.012.047.006c.053.006.105.013.156.021 1.2.181 1.827.784 1.827 2.012 0 1.217-.627 1.992-1.828 2.194v.001Z"
      fill="#000"
    />
  </svg>
);
