import { getDefaultConfig, RainbowKitProvider } from '@rainbow-me/rainbowkit';
import { WagmiProvider } from 'wagmi';
import { btr, btrTestnet } from 'wagmi/chains';

const config = getDefaultConfig({
  appName: 'BitNova Dashboard',
  projectId: 'af2938e2d16d40386a87b41441febf06',
  chains: [btr, btrTestnet],
  ssr: true,
});

export function WalletProvider({ children }: { children: React.ReactNode }) {
  return (
    <WagmiProvider config={config}>
      <RainbowKitProvider>{children}</RainbowKitProvider>
    </WagmiProvider>
  );
}
