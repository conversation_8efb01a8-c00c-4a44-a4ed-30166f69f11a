import Jazzicon from 'react-jazzicon';
import { ClientOnly } from 'remix-utils/client-only';

function jsNumberForAddress(address: string): number {
  const addr = address.slice(2, 10);
  const seed = parseInt(addr, 16);

  return seed;
}

export interface WalletAvatarProps {
  size?: number;
  address: string;
}

export function WalletAvatar({ size = 30, address }: WalletAvatarProps) {
  return (
    <ClientOnly>{() => <Jazzicon diameter={size} seed={jsNumberForAddress(address)} />}</ClientOnly>
  );
}
