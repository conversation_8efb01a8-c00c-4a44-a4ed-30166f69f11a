import type { Transaction } from '@/lib/api';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { shortenAddress } from '@/lib/utils';
import { format } from 'date-fns';
import { TZDate } from '@date-fns/tz';
import { Button } from './ui/button';
import { useSignMessage } from 'wagmi';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import ky from 'ky';
import { fromHex, type Hex } from 'viem';
import { bitlayerTestnet, suiTestnet } from '@/config/chains';

type TransactionCardProps = {
  transaction: Transaction;
};

function useSignTransaction() {
  const queryClient = useQueryClient();
  const { signMessageAsync } = useSignMessage();
  const mutation = useMutation({
    mutationFn: async ({ transaction }: { transaction: Transaction }) => {
      const message = fromHex(
        transaction.type === 'withdraw'
          ? (transaction.sign_msg as Hex)
          : `0x${transaction.sign_msg}`,
        { to: 'bytes' },
      );
      const signature = await signMessageAsync({
        message: { raw: message },
      });

      const data = await ky
        .post('/api/transaction/sign', {
          json: {
            hash: transaction.hash,
            signature,
            type: transaction.type,
          },
          timeout: 60000,
        })
        .json<Transaction>();

      queryClient.setQueryData(['signer/transactions'], (prev: Transaction[]) =>
        prev.map((t) => (t.hash === data.hash ? data : t)),
      );
    },
  });

  return {
    signTransaction: mutation.mutate,
    signTransactionAsync: mutation.mutateAsync,
    ...mutation,
  };
}

export function TransactionCard({ transaction }: TransactionCardProps) {
  const { signTransactionAsync, isPending } = useSignTransaction();

  const handleSign = async () => {
    signTransactionAsync({ transaction });
  };

  const [sourceChain, targetChain] =
    transaction.type === 'deposit' ? [bitlayerTestnet, suiTestnet] : [suiTestnet, bitlayerTestnet];

  return (
    <Card className="hover:border-primary duration-200">
      <CardHeader>
        <CardTitle className="uppercase">{transaction.type} Transaction</CardTitle>
        <CardDescription> </CardDescription>
      </CardHeader>
      <CardContent className="text-xs space-y-0.5">
        <TransactionField label="Hash">
          {shortenAddress(transaction.hash, { prefixLength: 10, suffixLength: 8 })}
        </TransactionField>
        {transaction.status === 'finished' && (
          <TransactionField label="Target Hash">
            {shortenAddress(transaction.target_hash, { prefixLength: 10, suffixLength: 8 })}
          </TransactionField>
        )}
        <TransactionField label="From">
          {shortenAddress(transaction.from_address, { prefixLength: 10, suffixLength: 8 })}
        </TransactionField>
        <TransactionField label="Original Amount">{transaction.original_amount}</TransactionField>
        <TransactionField label="Amount">{transaction.amount}</TransactionField>
        <TransactionField label="Fee">{transaction.fee}</TransactionField>
        <TransactionField label="Status">
          <TransactionStatus status={transaction.status} />
        </TransactionField>
        <TransactionField label="Created At">
          {formatTransactionTime(transaction.created_at)}
        </TransactionField>
        <TransactionField label="Updated At">
          {formatTransactionTime(transaction.updated_at)}
        </TransactionField>
        <TransactionField label="Current Process">{transaction.current_process}</TransactionField>
        <TransactionField label="Total Process">{transaction.total_process}</TransactionField>
        <TransactionField label="Type">{transaction.type}</TransactionField>
        <TransactionField label="Sign Message">
          {shortenAddress(transaction.sign_msg, { prefixLength: 12, suffixLength: 10 })}
        </TransactionField>
      </CardContent>
      <CardFooter>
        {transaction.status === 'new' && (
          <Button size="sm" className="w-20" onClick={handleSign} disabled={isPending}>
            Sign
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

function formatTransactionTime(timestamp: number) {
  const date = new TZDate(timestamp * 1000, 'UTC');
  return format(date, 'yyyy-MM-dd HH:mm:ss XXX');
}

const statusVariants: Record<Transaction['status'], React.ComponentProps<typeof Badge>['variant']> =
  {
    new: 'info',
    signed: 'secondary',
    finished: 'success',
    failed: 'destructive',
  };

function TransactionStatus({ status }: { status: Transaction['status'] }) {
  return <Badge variant={statusVariants[status]}>{status}</Badge>;
}

function TransactionField({ label, children }: { label: string; children: React.ReactNode }) {
  return (
    <p className="h-6">
      <span className="font-bold">{label}:</span> {children}
    </p>
  );
}
