import type { Transaction } from '@/lib/api';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { shortenAddress } from '@/lib/utils';
import { format } from 'date-fns';
import { TZDate } from '@date-fns/tz';
import { Button } from './ui/button';
import { useSignMessage } from 'wagmi';
import { ethers } from 'ethers';
import { useMutation } from '@tanstack/react-query';
import ky from 'ky';

type TransactionCardProps = {
  transaction: Transaction;
};

function useSignTransaction() {
  const { signMessageAsync } = useSignMessage();
  const mutation = useMutation({
    mutationFn: async ({ transaction }: { transaction: Transaction }) => {
      const signature = await signMessageAsync({
        message: { raw: ethers.getBytes(transaction.sign_msg) },
      });

      await ky.post('/api/transaction/sign', {
        json: {
          hash: transaction.hash,
          signature,
          type: transaction.type,
        },
      });
    },
  });

  return {
    signTransaction: mutation.mutate,
    signTransactionAsync: mutation.mutateAsync,
    ...mutation,
  };
}

export function TransactionCard({ transaction }: TransactionCardProps) {
  const { signTransactionAsync, isP } = useSignTransaction();

  const handleSign = async () => {
    signTransactionAsync({ transaction });
  };

  const handleVerify = async () => {
    const messageHash = transaction.sign_msg;
    const signatureHex =
      // '0x4894194cba597b74915762c4cab69308528f8fb3ede254a279ffbbc196f694316b5d02e218510009c011894a23ff25e7cb072d45a30a4b3aa7197ca70393fe321b';
      // '0x0595281a09c4e30866c5db5e7ad73a5af11a73270a266646d3b4a1b8a687af0879bd48a06da4a1d6e0f85c9abef84d110d0e21c9f426a41181300af829ffad081c';
      // '0x98094d4310b0d5c84ccfa23d02390d58849d77cbe035f99220e4ef7e1beb39b93945a5d47451821b2e2d7023579adae96f3bafdb0dfe98b799dd25cc4481bd911c';
      '0xbe2df64d7edf367bf8bbe1172e015f997aaf05772d3f2bce12c6936466d0ea94315759f5beeaa00c5f0b622c203e1ff220f05021eb6ed58282107826f78faac31c';
    const recoveredAddress = ethers.recoverAddress(ethers.getBytes(messageHash), signatureHex);
    // const recoveredAddress = ethers.verifyMessage(ethers.getBytes(messageHash), signatureHex);
    // const recoveredAddress = ethers.verifyMessage(messageHash, signatureHex);

    console.log(recoveredAddress);
    // ******************************************
  };

  return (
    <Card className="hover:border-primary duration-200">
      <CardHeader>
        <CardTitle className="uppercase">{transaction.type} Transaction</CardTitle>
        <CardDescription> </CardDescription>
      </CardHeader>
      <CardContent className="text-xs space-y-0.5">
        <TransactionField label="Hash">
          {shortenAddress(transaction.hash, { prefixLength: 10, suffixLength: 8 })}
        </TransactionField>
        <TransactionField label="From">
          {shortenAddress(transaction.from_address, { prefixLength: 10, suffixLength: 8 })}
        </TransactionField>
        <TransactionField label="Original Amount">{transaction.original_amount}</TransactionField>
        <TransactionField label="Amount">{transaction.amount}</TransactionField>
        <TransactionField label="Fee">{transaction.fee}</TransactionField>
        <TransactionField label="Status">
          <TransactionStatus status={transaction.status} />
        </TransactionField>
        <TransactionField label="Created At">
          {formatTransactionTime(transaction.created_at)}
        </TransactionField>
        <TransactionField label="Updated At">
          {formatTransactionTime(transaction.updated_at)}
        </TransactionField>
        <TransactionField label="Current Process">{transaction.current_process}</TransactionField>
        <TransactionField label="Total Process">{transaction.total_process}</TransactionField>
        <TransactionField label="Type">{transaction.type}</TransactionField>
        <TransactionField label="Sign Message">
          {shortenAddress(transaction.sign_msg, { prefixLength: 12, suffixLength: 10 })}
        </TransactionField>
      </CardContent>
      <CardFooter>
        {transaction.status === 'new' && (
          <Button size="sm" className="w-20" onClick={handleSign} disabled={isPending}>
            Sign
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

function formatTransactionTime(timestamp: number) {
  const date = new TZDate(timestamp * 1000, 'UTC');
  return format(date, 'yyyy-MM-dd HH:mm:ss XXX');
}

const statusVariants: Record<Transaction['status'], React.ComponentProps<typeof Badge>['variant']> =
  {
    new: 'info',
    signed: 'secondary',
    finished: 'success',
    failed: 'destructive',
  };

function TransactionStatus({ status }: { status: Transaction['status'] }) {
  return <Badge variant={statusVariants[status]}>{status}</Badge>;
}

function TransactionField({ label, children }: { label: string; children: React.ReactNode }) {
  return (
    <p className="h-6">
      <span className="font-bold">{label}:</span> {children}
    </p>
  );
}
